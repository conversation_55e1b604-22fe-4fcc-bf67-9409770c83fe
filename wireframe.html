<!DOCTYPE html>
<html lang="id" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Antosa Architect - Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        /* Wireframe styling */
        .wireframe-box {
            border: 2px solid #666;
            background-color: #fff;
            position: relative;
        }

        .wireframe-text {
            background-color: #ddd;
            height: 20px;
            margin: 5px 0;
        }

        .wireframe-text.large {
            height: 40px;
        }

        .wireframe-text.small {
            height: 15px;
        }

        .wireframe-image {
            background-color: #ccc;
            border: 2px dashed #999;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .wireframe-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }

        .wireframe-button.secondary {
            background-color: #6c757d;
        }

        /* Layout containers */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .grid {
            display: grid;
            gap: 20px;
        }

        .grid-2 { grid-template-columns: 1fr 1fr; }
        .grid-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-4 { grid-template-columns: repeat(4, 1fr); }

        .flex {
            display: flex;
            gap: 20px;
        }

        .flex-center {
            justify-content: center;
            align-items: center;
        }

        .flex-between {
            justify-content: space-between;
            align-items: center;
        }

        /* Sections */
        section {
            padding: 60px 0;
            border-bottom: 1px solid #ddd;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: white;
            border-bottom: 2px solid #666;
            z-index: 100;
            padding: 15px 0;
        }

        .logo {
            width: 150px;
            height: 40px;
        }

        /* Navigation */
        .dock-nav {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            border: 2px solid #666;
            border-radius: 20px;
            padding: 10px;
            z-index: 100;
        }

        .nav-items {
            display: flex;
            gap: 10px;
        }

        .nav-item {
            width: 50px;
            height: 50px;
            border: 2px solid #666;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            text-decoration: none;
            color: #666;
            font-size: 12px;
        }

        .nav-item.active {
            background-color: #007bff;
            color: white;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background-color: #e9ecef;
            margin-top: 70px;
            position: relative;
        }

        .hero-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 100%;
        }

        .hero-title {
            width: 400px;
            height: 80px;
            margin: 0 auto 20px;
        }

        .hero-subtitle {
            width: 600px;
            height: 30px;
            margin: 0 auto 30px;
        }

        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        /* Cards */
        .card {
            padding: 20px;
            margin-bottom: 20px;
        }

        .card-header {
            margin-bottom: 15px;
        }

        .card-content {
            margin-bottom: 15px;
        }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 40px 0;
        }

        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }

        .stat-number {
            width: 80px;
            height: 40px;
            margin: 0 auto 10px;
        }

        .stat-label {
            width: 120px;
            height: 20px;
            margin: 0 auto;
        }

        /* Team Grid */
        .team-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .team-member {
            text-align: center;
            padding: 20px;
        }

        .team-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 15px;
        }

        .team-name {
            width: 150px;
            height: 25px;
            margin: 0 auto 10px;
        }

        .team-role {
            width: 120px;
            height: 20px;
            margin: 0 auto 10px;
        }

        .team-bio {
            width: 100%;
            height: 60px;
            margin: 0 auto;
        }

        /* Services Grid */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .service-card {
            padding: 30px 20px;
            text-align: center;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            margin: 0 auto 20px;
        }

        .service-title {
            width: 180px;
            height: 25px;
            margin: 0 auto 15px;
        }

        .service-description {
            width: 100%;
            height: 80px;
            margin: 0 auto;
        }

        /* Portfolio Grid */
        .portfolio-filters {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 40px 0;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #666;
            background-color: white;
            cursor: pointer;
        }

        .filter-btn.active {
            background-color: #007bff;
            color: white;
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 40px 0;
        }

        .portfolio-item {
            aspect-ratio: 1;
        }

        /* FAQ */
        .faq-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin: 40px 0;
        }

        .faq-categories {
            padding: 20px;
        }

        .faq-category {
            width: 100%;
            height: 40px;
            margin-bottom: 10px;
            border: 2px solid #666;
            background-color: white;
        }

        .faq-questions {
            padding: 20px;
        }

        .faq-question {
            width: 100%;
            height: 50px;
            margin-bottom: 15px;
            border: 2px solid #666;
            background-color: white;
        }

        /* Contact */
        .contact-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }

        .contact-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-input {
            width: 100%;
            height: 40px;
            border: 2px solid #666;
            background-color: white;
        }

        .form-textarea {
            width: 100%;
            height: 100px;
            border: 2px solid #666;
            background-color: white;
        }

        .contact-info {
            padding: 30px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .contact-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            border-radius: 5px;
        }

        .contact-text {
            width: 200px;
            height: 20px;
        }

        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            padding: 60px 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h3 {
            color: white;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .footer-link {
            width: 150px;
            height: 15px;
            background-color: #6c757d;
            margin-bottom: 10px;
        }

        .footer-bottom {
            border-top: 1px solid #6c757d;
            padding-top: 20px;
            text-align: center;
        }

        .footer-copyright {
            width: 300px;
            height: 20px;
            background-color: #6c757d;
            margin: 0 auto;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
            
            .stats-grid, .team-grid, .services-grid {
                grid-template-columns: 1fr;
            }
            
            .portfolio-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .faq-container, .contact-container, .footer-grid {
                grid-template-columns: 1fr;
            }
            
            .dock-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="flex flex-between">
                <div class="wireframe-image logo">LOGO</div>
            </div>
        </div>
    </header>

    <!-- Dock Navigation -->
    <nav class="dock-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">HOME</a>
            <a href="#about" class="nav-item">ABOUT</a>
            <a href="#services" class="nav-item">SERVICES</a>
            <a href="#portfolio" class="nav-item">PORTFOLIO</a>
            <a href="#clients" class="nav-item">CLIENTS</a>
            <a href="#faq" class="nav-item">FAQ</a>
            <a href="#contact" class="nav-item">CONTACT</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero wireframe-box">
        <div class="hero-content">
            <div class="wireframe-image" style="width: 100px; height: 30px; margin: 0 auto 20px;">BADGE</div>
            <div class="wireframe-text large hero-title"></div>
            <div class="wireframe-text hero-subtitle"></div>
            <div class="hero-buttons">
                <button class="wireframe-button">PRIMARY CTA</button>
                <button class="wireframe-button secondary">SECONDARY CTA</button>
            </div>
        </div>
        <div style="position: absolute; bottom: 20px; right: 20px;">
            <div class="wireframe-box" style="padding: 10px; background: white;">
                <div style="display: flex; gap: 10px; align-items: center;">
                    <div style="width: 30px; height: 30px; border: 2px solid #666;">&lt;</div>
                    <div style="width: 60px; height: 20px; background: #ddd;"></div>
                    <div style="width: 30px; height: 30px; border: 2px solid #666;">&gt;</div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 150px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 400px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- About Content Grid -->
            <div class="grid grid-2" style="margin-bottom: 60px;">
                <!-- Left Content -->
                <div>
                    <div class="wireframe-text large" style="margin-bottom: 20px;"></div>
                    <div class="wireframe-text" style="margin-bottom: 15px;"></div>
                    <div class="wireframe-text" style="margin-bottom: 15px;"></div>
                    <div class="wireframe-text" style="width: 80%; margin-bottom: 15px;"></div>
                </div>

                <!-- Right Content - Vision & Mission -->
                <div>
                    <!-- Vision Card -->
                    <div class="wireframe-box card" style="margin-bottom: 20px;">
                        <div class="card-header">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div class="wireframe-image" style="width: 30px; height: 30px;"></div>
                                <div class="wireframe-text" style="width: 80px; height: 20px;"></div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="wireframe-text" style="margin-bottom: 10px;"></div>
                            <div class="wireframe-text" style="width: 90%;"></div>
                        </div>
                    </div>

                    <!-- Mission Card -->
                    <div class="wireframe-box card">
                        <div class="card-header">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div class="wireframe-image" style="width: 30px; height: 30px;"></div>
                                <div class="wireframe-text" style="width: 80px; height: 20px;"></div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="wireframe-text small" style="margin-bottom: 8px;"></div>
                            <div class="wireframe-text small" style="margin-bottom: 8px;"></div>
                            <div class="wireframe-text small" style="width: 85%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-grid">
                <div class="wireframe-box stat-card">
                    <div class="wireframe-image stat-number">ICON</div>
                    <div class="wireframe-text stat-number"></div>
                    <div class="wireframe-text stat-label"></div>
                </div>
                <div class="wireframe-box stat-card">
                    <div class="wireframe-image stat-number">ICON</div>
                    <div class="wireframe-text stat-number"></div>
                    <div class="wireframe-text stat-label"></div>
                </div>
                <div class="wireframe-box stat-card">
                    <div class="wireframe-image stat-number">ICON</div>
                    <div class="wireframe-text stat-number"></div>
                    <div class="wireframe-text stat-label"></div>
                </div>
            </div>

            <!-- Team Section -->
            <div style="text-align: center; margin: 60px 0 40px;">
                <div class="wireframe-text small" style="width: 150px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 350px; margin: 0 auto;"></div>
            </div>

            <div class="team-grid">
                <div class="wireframe-box team-member">
                    <div class="wireframe-image team-avatar">PHOTO</div>
                    <div class="wireframe-text team-name"></div>
                    <div class="wireframe-text team-role"></div>
                    <div class="wireframe-text team-bio"></div>
                    <div style="display: flex; justify-content: center; gap: 10px; margin-top: 15px;">
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                    </div>
                </div>
                <div class="wireframe-box team-member">
                    <div class="wireframe-image team-avatar">PHOTO</div>
                    <div class="wireframe-text team-name"></div>
                    <div class="wireframe-text team-role"></div>
                    <div class="wireframe-text team-bio"></div>
                    <div style="display: flex; justify-content: center; gap: 10px; margin-top: 15px;">
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                    </div>
                </div>
                <div class="wireframe-box team-member">
                    <div class="wireframe-image team-avatar">PHOTO</div>
                    <div class="wireframe-text team-name"></div>
                    <div class="wireframe-text team-role"></div>
                    <div class="wireframe-text team-bio"></div>
                    <div style="display: flex; justify-content: center; gap: 10px; margin-top: 15px;">
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                        <div class="wireframe-image" style="width: 30px; height: 30px; border-radius: 50%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 120px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 350px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- Services Grid -->
            <div class="services-grid">
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="wireframe-box service-card">
                    <div class="wireframe-image service-icon">ICON</div>
                    <div class="wireframe-text service-title"></div>
                    <div class="wireframe-text service-description"></div>
                    <div style="margin-top: 15px;">
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="margin-bottom: 5px;"></div>
                        <div class="wireframe-text small" style="width: 80%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 120px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 300px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- Search Bar -->
            <div style="text-align: center; margin-bottom: 30px;">
                <div class="wireframe-box" style="width: 400px; height: 50px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    SEARCH BAR
                </div>
            </div>

            <!-- Portfolio Filters -->
            <div class="portfolio-filters">
                <button class="filter-btn active">ALL</button>
                <button class="filter-btn">RESIDENTIAL</button>
                <button class="filter-btn">COMMERCIAL</button>
                <button class="filter-btn">INTERIOR</button>
                <button class="filter-btn">RENOVATION</button>
            </div>

            <!-- View Options -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px;">
                <div style="display: flex; gap: 10px;">
                    <div class="wireframe-text small" style="width: 80px;"></div>
                    <div style="display: flex; gap: 5px;">
                        <button class="wireframe-button" style="padding: 5px 15px;">GRID</button>
                        <button class="wireframe-button secondary" style="padding: 5px 15px;">LIST</button>
                    </div>
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <div class="wireframe-text small" style="width: 80px;"></div>
                    <div class="wireframe-box" style="width: 120px; height: 35px;"></div>
                </div>
            </div>

            <!-- Portfolio Grid -->
            <div class="portfolio-grid">
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 1</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 2</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 3</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 4</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 5</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 6</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 7</div>
                <div class="wireframe-box portfolio-item wireframe-image">PROJECT 8</div>
            </div>
        </div>
    </section>

    <!-- Clients Section -->
    <section id="clients">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 100px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 250px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- Client Showcase -->
            <div style="text-align: center;">
                <div class="wireframe-box wireframe-image" style="width: 100%; height: 400px; margin: 0 auto;">
                    CLIENT SHOWCASE IMAGE
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 80px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 300px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- FAQ Container -->
            <div class="faq-container">
                <!-- Categories Sidebar -->
                <div class="wireframe-box faq-categories">
                    <div style="margin-bottom: 20px;">
                        <div class="wireframe-text" style="width: 120px; margin-bottom: 15px;"></div>
                    </div>
                    <div class="faq-category wireframe-box" style="background-color: #007bff; color: white; display: flex; align-items: center; justify-content: center;">GENERAL</div>
                    <div class="faq-category wireframe-box" style="display: flex; align-items: center; justify-content: center;">DESIGN</div>
                    <div class="faq-category wireframe-box" style="display: flex; align-items: center; justify-content: center;">COST</div>
                    <div class="faq-category wireframe-box" style="display: flex; align-items: center; justify-content: center;">CONSTRUCTION</div>
                </div>

                <!-- Questions -->
                <div class="wireframe-box faq-questions">
                    <div class="faq-question wireframe-box" style="display: flex; align-items: center; justify-content: space-between; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;">?</div>
                            <div class="wireframe-text" style="width: 300px;"></div>
                        </div>
                        <div class="wireframe-image" style="width: 30px; height: 30px;">+</div>
                    </div>
                    <div class="faq-question wireframe-box" style="display: flex; align-items: center; justify-content: space-between; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;">?</div>
                            <div class="wireframe-text" style="width: 350px;"></div>
                        </div>
                        <div class="wireframe-image" style="width: 30px; height: 30px;">+</div>
                    </div>
                    <div class="faq-question wireframe-box" style="display: flex; align-items: center; justify-content: space-between; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;">?</div>
                            <div class="wireframe-text" style="width: 280px;"></div>
                        </div>
                        <div class="wireframe-image" style="width: 30px; height: 30px;">+</div>
                    </div>
                    <div class="faq-question wireframe-box" style="display: flex; align-items: center; justify-content: space-between; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;">?</div>
                            <div class="wireframe-text" style="width: 320px;"></div>
                        </div>
                        <div class="wireframe-image" style="width: 30px; height: 30px;">+</div>
                    </div>
                    <div class="faq-question wireframe-box" style="display: flex; align-items: center; justify-content: space-between; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;">?</div>
                            <div class="wireframe-text" style="width: 290px;"></div>
                        </div>
                        <div class="wireframe-image" style="width: 30px; height: 30px;">+</div>
                    </div>
                </div>
            </div>

            <!-- Contact CTA -->
            <div style="text-align: center; margin-top: 60px;">
                <div class="wireframe-text" style="width: 400px; margin: 0 auto 20px;"></div>
                <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                    <div class="wireframe-text" style="width: 150px;"></div>
                    <div class="wireframe-image" style="width: 20px; height: 20px;">→</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact">
        <div class="container">
            <!-- Section Header -->
            <div style="text-align: center; margin-bottom: 60px;">
                <div class="wireframe-text small" style="width: 100px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text large" style="width: 250px; margin: 0 auto 20px;"></div>
                <div class="wireframe-text" style="width: 600px; margin: 0 auto;"></div>
            </div>

            <!-- Contact Container -->
            <div class="contact-container">
                <!-- Contact Form -->
                <div class="wireframe-box contact-form">
                    <div style="margin-bottom: 30px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div class="wireframe-image" style="width: 30px; height: 30px;"></div>
                            <div class="wireframe-text" style="width: 120px;"></div>
                        </div>
                    </div>

                    <div class="grid grid-2" style="margin-bottom: 20px;">
                        <div class="form-group">
                            <div class="wireframe-text small" style="width: 100px; margin-bottom: 5px;"></div>
                            <div class="form-input wireframe-box"></div>
                        </div>
                        <div class="form-group">
                            <div class="wireframe-text small" style="width: 120px; margin-bottom: 5px;"></div>
                            <div class="form-input wireframe-box"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                        <div class="form-input wireframe-box"></div>
                    </div>

                    <div class="form-group">
                        <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                        <div class="form-textarea wireframe-box"></div>
                    </div>

                    <button class="wireframe-button" style="width: 100%; padding: 15px;">SEND MESSAGE</button>
                </div>

                <!-- Contact Info -->
                <div class="wireframe-box contact-info">
                    <div class="contact-item">
                        <div class="wireframe-image contact-icon">📞</div>
                        <div>
                            <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text"></div>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="wireframe-image contact-icon">✉️</div>
                        <div>
                            <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text"></div>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="wireframe-image contact-icon">📍</div>
                        <div>
                            <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text" style="margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text" style="margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text"></div>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="wireframe-image contact-icon">🕒</div>
                        <div>
                            <div class="wireframe-text small" style="width: 80px; margin-bottom: 5px;"></div>
                            <div class="wireframe-text contact-text"></div>
                        </div>
                    </div>

                    <!-- Map -->
                    <div class="wireframe-box wireframe-image" style="width: 100%; height: 200px; margin-top: 30px;">
                        GOOGLE MAPS
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <!-- Footer Grid -->
            <div class="footer-grid">
                <!-- Company Section -->
                <div class="footer-section">
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                        <div class="wireframe-image" style="width: 40px; height: 40px; background-color: #007bff;">LOGO</div>
                        <h3>COMPANY NAME</h3>
                    </div>
                    <div class="wireframe-text" style="background-color: #6c757d; margin-bottom: 10px;"></div>
                    <div class="wireframe-text" style="background-color: #6c757d; width: 80%; margin-bottom: 20px;"></div>

                    <!-- Social Links -->
                    <div style="display: flex; gap: 10px;">
                        <div class="wireframe-image" style="width: 35px; height: 35px; border-radius: 8px; background-color: #6c757d;">FB</div>
                        <div class="wireframe-image" style="width: 35px; height: 35px; border-radius: 8px; background-color: #6c757d;">IG</div>
                        <div class="wireframe-image" style="width: 35px; height: 35px; border-radius: 8px; background-color: #6c757d;">WA</div>
                        <div class="wireframe-image" style="width: 35px; height: 35px; border-radius: 8px; background-color: #6c757d;">YT</div>
                    </div>
                </div>

                <!-- Navigation Section -->
                <div class="footer-section">
                    <h3>NAVIGATION</h3>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                </div>

                <!-- Services Section -->
                <div class="footer-section">
                    <h3>SERVICES</h3>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                    <div class="footer-link"></div>
                </div>

                <!-- Contact Section -->
                <div class="footer-section">
                    <h3>CONTACT</h3>
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                        <div class="wireframe-image" style="width: 25px; height: 25px; background-color: #6c757d;">📞</div>
                        <div class="footer-link" style="width: 120px;"></div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                        <div class="wireframe-image" style="width: 25px; height: 25px; background-color: #6c757d;">✉️</div>
                        <div class="footer-link" style="width: 140px;"></div>
                    </div>
                    <div style="display: flex; align-items: flex-start; gap: 10px; margin-bottom: 15px;">
                        <div class="wireframe-image" style="width: 25px; height: 25px; background-color: #6c757d;">📍</div>
                        <div>
                            <div class="footer-link" style="width: 120px; margin-bottom: 5px;"></div>
                            <div class="footer-link" style="width: 100px; margin-bottom: 5px;"></div>
                            <div class="footer-link" style="width: 130px;"></div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div class="wireframe-image" style="width: 25px; height: 25px; background-color: #6c757d;">🕒</div>
                        <div class="footer-link" style="width: 100px;"></div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-copyright"></div>
            </div>
        </div>
    </footer>

</body>
</html>
